# AI-Ops SRE 助手

这是一个基于 CrewAI 框架构建的多智能体（Multi-Agent）系统，旨在自动化诊断和修复 Kubernetes (k8s) 中的 OOM (Out-of-Memory) 问题。该系统采用服务化架构，并通过一个命令行界面（CLI）实现了完整的人机协同（Human-in-the-Loop）审批流程。

## 架构演进之路：一次宝贵的探索

本项目的核心挑战在于如何在客户端-服务器（C/S）架构中，优雅地实现需要人工审批的暂停和恢复功能。我们的探索过程揭示了关于 CrewAI 框架的重要经验：

1.  **最初的尝试与陷阱**: 我们最初的实现是一个单一的 Crew，并尝试使用 `human_input=True` 参数来暂停工作流以等待用户批准。然而，在测试中我们发现，这个参数会**阻塞**运行 Crew 的进程来等待终端输入。当 Crew 在后台的 FastAPI 服务器中运行时，这导致了流程被**永久挂起**，因为后台进程无法接收到来自用户终端的输入。

2.  **错误的弯路：混淆开源版与商业版**: 在查阅文档时，我们发现了一个基于 Webhook 和 Resume API 的优雅异步 HITL 机制。我们曾一度认为这是解决方案，但经过更深入的研究后发现，这是 **CrewAI Enterprise（商业版）** 的专属功能。这个发现至关重要，它提醒我们必须精确区分开源库和其商业版本的功能边界。

3.  **最终的正确架构：工作流拆分**: 在排除了不适用的方案后，我们回归到问题的本质，并设计出了一个健壮、解耦且完全基于开源库能力的架构。我们将原本单一、连续的工作流**拆分为两个独立的、原子化的 Crew**：
    *   **诊断 Crew**: 负责完成从数据收集到分析提议的所有工作。
    *   **执行 Crew**: 只负责接收一个明确的指令并执行它。

    API 服务器 (`main.py`) 在两者之间扮演了**状态管理器**的角色，而 CLI (`cli.py`) 则负责呈现信息并获取用户输入，从而完美地实现了人机协同。

## 最终架构

我们的系统由三个核心组件构成：

```mermaid
graph TD
    subgraph "用户终端"
        A[CLI 客户端 (cli.py)]
    end

    subgraph "应用服务器"
        B[FastAPI 服务 (main.py)]
        C[诊断 Crew (diagnose_crew.py)]
        D[执行 Crew (execute_crew.py)]
    end

    subgraph "工具服务"
        E[MCP 工具服务器 (mcp_server.py)]
    end

    A -- 1. 发起诊断 (POST /diagnose) --> B
    B -- 2. 启动 --> C
    C -- 自动启动/关闭 --> E
    B -- 3. 轮询状态 (GET /status) --> A
    A -- 4. 用户输入'yes' --> B
    B -- 5. 发起执行 (POST /execute) --> D
    D -- 自动启动/关闭 --> E
    B -- 6. 轮询状态 (GET /status) --> A
    A -- 7. 显示最终结果 --> A
```

1.  **FastAPI 服务 (`main.py`)**: 作为系统的核心，提供异步 API 来启动和管理 Crew 的生命周期。
2.  **双 Crew 模型 (`diagnose_crew.py`, `execute_crew.py`)**: 将复杂的任务流拆分为两个独立的、职责单一的 Crew，大大提升了系统的模块化和可维护性。
3.  **CLI 客户端 (`cli.py`)**: 为 SRE 工程师提供了一个与系统交互的、用户友好的命令行界面。

## 如何运行

1.  **安装依赖**:
    ```bash
    # 确保你已经安装了 uv 和 python 环境
    uv pip install -r requirements.txt
    ```

2.  **启动应用服务器**:
    在项目根目录下打开一个终端，运行 FastAPI 服务：
    ```bash
    uvicorn main:app --reload
    ```

3.  **运行客户端**:
    打开**另一个**终端，通过 CLI 客户端发起一个 OOM 诊断任务：
    ```bash
    python cli.py --namespace default
    ```
    - 程序将首先执行诊断流程。
    - 当诊断完成后，它会打印出修复建议并等待你的输入。
    - 输入 `yes` 并按回车，程序将继续执行修复流程。

## 可扩展性

本架构具有良好的可扩展性。要添加一个新的场景（例如“成本分析”），你只需：
1.  在 `config/` 目录下创建一个新的场景文件夹，例如 `cost_analysis/`。
2.  在该文件夹中，定义新的 `agents.yaml` 和 `tasks.yaml`。
3.  在 `cli.py` 中，通过 `--scenario cost_analysis` 参数即可调用新的 Crew。
## 运行应用

要启动此应用，请运行以下命令：

```bash
./run.sh
```

这将使用 `uvicorn` 在 `http://localhost:8000` 上启动应用。