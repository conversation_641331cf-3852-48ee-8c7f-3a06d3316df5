import os
import yaml
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter, ScrapeWebsiteTool
from mcp import StdioServerParameters

load_dotenv()

# 初始化人机交互工具
human_input_tool = ScrapeWebsiteTool()

def load_yaml(path: str) -> dict:
    with open(path, 'r') as file:
        return yaml.safe_load(file)

def create_and_run_crew(inputs: dict, scenario: str = "oom_diagnose"):
    """
    创建并运行 AI-Ops SRE Crew。
    """
    # 用于保存服务器配置的列表，方便将来添加更多服务器。
    server_configs = [
        StdioServerParameters(command="python", args=["mcp_server.py"])
    ]
    
    # 使用第一个服务器配置初始化适配器。
    # 如需使用多个服务器，可创建多个适配器并组合其工具。
    with MCPServerAdapter(server_configs[0]) as mcp_tools:
        print("MCP 服务器连接已建立。")

        qwen_llm = LLM(
            model="openai/qwen-turbo-latest",
            base_url=os.getenv("QWEN_BASE_URL"),
            api_key=os.getenv("QWEN_API_KEY"),
            temperature=0.7,
        )

        # 根据场景加载配置
        agents_config = load_yaml(f'config/{scenario}/agents.yaml')
        tasks_config = load_yaml(f'config/{scenario}/tasks.yaml')

        # --- 创建代理 ---
        sre_supervisor = Agent(**agents_config['sre_supervisor'], llm=qwen_llm, verbose=True, tools=[human_input_tool])
        k8s_diagnoser = Agent(**agents_config['k8s_diagnoser'], tools=[mcp_tools["k8s_get"], mcp_tools["k8s_describe"]], llm=qwen_llm, verbose=True)
        sls_diagnoser = Agent(**agents_config['sls_diagnoser'], tools=[mcp_tools["query_sls"]], llm=qwen_llm, verbose=True)
        arms_diagnoser = Agent(**agents_config['arms_diagnoser'], tools=[mcp_tools["query_arms"]], llm=qwen_llm, verbose=True)
        cluster_operator = Agent(**agents_config['cluster_operator'], tools=[mcp_tools["k8s_patch_deployment_resources"], mcp_tools["cs_autoscaler_scale_nodepool"]], llm=qwen_llm, verbose=True)

        # --- 创建并行诊断任务 ---
        k8s_task = Task(**tasks_config['k8s_investigation_task'], agent=k8s_diagnoser)
        sls_task = Task(**tasks_config['sls_log_analysis_task'], agent=sls_diagnoser)
        arms_task = Task(**tasks_config['arms_metric_analysis_task'], agent=arms_diagnoser)

        # --- 创建主管的分析与规划任务 ---
        analysis_propose_task = Task(
            description="""分析来自 Kubernetes、SLS 和 ARMS 诊断任务收集的数据。
            基于综合证据，确定 OOMKill 问题的根本原因。
            按照 SRE 手册的“提议”部分，提出明确可执行的方案。
            如果需要修复，方案必须为操作员提供完整的一步执行指令。
            如果未发现问题，请明确说明并给出长期建议。
            你的最终输出必须是给操作员的方案，或者是问题结论。""",
            expected_output="最终报告，包含根因分析和精确、可直接执行的方案或长期建议。",
            agent=sre_supervisor,
            context=[k8s_task, sls_task, arms_task] # 此任务依赖于并行任务
        )

        # --- 创建 HITL 审批任务 ---
        approval_task = Task(
            description=f"""你必须使用 human_input_tool 工具。
            分析师已经提出了一个方案，你需要征求人类的批准。
            将以下方案呈现给用户，并询问他们是否批准：
            '{{{{analysis_propose_task}}}}'
            等待用户的回复。""",
            expected_output="用户的批准或拒绝。只应包含 'yes' 或 'no'。",
            agent=sre_supervisor,
            context=[analysis_propose_task],
            human_input=True
        )

        # --- 创建修复任务 (带条件逻辑) ---
        remediation_task = Task(
            description="""根据 '{{approval_task}}' 的最终输出决定是否执行操作。
            - 如果输出是 'yes'，你必须严格执行 '{{analysis_propose_task}}' 中提出的方案。
            - 如果输出是 'no'，你必须中止操作，并报告“操作已被用户取消”。""",
            expected_output="一份简洁的执行报告，说明已执行的操作和结果，或明确报告操作已被用户取消。",
            agent=cluster_operator,
            context=[approval_task, analysis_propose_task] # 修复任务需要审批结果和原始方案
        )
        
        # --- 组装团队 ---
        crew = Crew(
            agents=[sre_supervisor, k8s_diagnoser, sls_diagnoser, arms_diagnoser, cluster_operator],
            tasks=[k8s_task, sls_task, arms_task, analysis_propose_task, approval_task, remediation_task], # 任务将根据依赖关系执行
            process=Process.hierarchical,
            manager_llm=qwen_llm,
            planning=True,
            planning_llm=qwen_llm,
            verbose=True
        )
        
        print("\n开始启动团队进行 OOMKill 问题排查...")
        result = crew.kickoff(inputs=inputs)
        return result

if __name__ == "__main__":
    # 这是一个用于直接测试 crew.py 的示例
    test_inputs = {
        "namespace": "default",
        "sls_project": "my-k8s-project",
        "sls_logstore": "container_logs",
        "arms_project": "my-arms-project",
        "regionId": "cn-hangzhou",
        "clusterID": "c123456789",
        "nodePoolID": "np123456789"
    }
    final_result = create_and_run_crew(test_inputs)
    print("\n\n## 团队执行完毕！")
    print("--------------------------")
    print(final_result)