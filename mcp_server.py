import logging
import json
from fastmcp import FastMCP

logging.basicConfig(level=logging.INFO, format='%(asctime)s - [MCP_SERVER] - %(levelname)s - %(message)s')

mcp = FastMCP(name="AI-Ops-SRE-Tools")

# --- K8s Tools ---
@mcp.tool
def k8s_get(resource_type: str, namespace: str = "default", flags: str = "") -> str:
    """Gets Kubernetes resources and returns them in a JSON formatted string list."""
    logging.info(f"Executing: kubectl get {resource_type} -n {namespace} {flags}")
    if "OOMKilled" in flags or "CrashLoopBackOff" in flags:
        mock_pods = ["app-pod-1-abcdef", "data-pod-2-ghijk"]
        logging.info(f"Found OOMKilled pods: {mock_pods}")
        return json.dumps(mock_pods)
    return json.dumps([f"mock-resource-{i}" for i in range(3)])

@mcp.tool
def k8s_describe(resource_type: str, resource_name: str, namespace: str = "default") -> str:
    """Describes a Kubernetes resource."""
    logging.info(f"Executing: kubectl describe {resource_type} {resource_name} -n {namespace}")
    return f"Mock Result for 'describe {resource_type} {resource_name}':\nEvents:\n  - Last state: Terminated, Reason: OOMKilled\n  - Memory usage exceeded limit."

@mcp.tool
def k8s_patch_deployment_resources(deployment_name: str, memory_limit: str, namespace: str = "default") -> str:
    """Patches a deployment's resource limits."""
    logging.info(f"Executing: kubectl patch deployment {deployment_name} in {namespace} with memory limit {memory_limit}")
    return f"Mock Success: Deployment '{deployment_name}' patched with memory limit '{memory_limit}'."

# --- Cloud Service Tools ---
@mcp.tool
def cs_autoscaler_scale_nodepool(nodepool_id: str, cluster_id: str, num_nodes: int) -> str:
    """Scales a node pool in the cluster."""
    logging.info(f"Executing: Scaling nodepool {nodepool_id} in cluster {cluster_id} to {num_nodes} nodes.")
    return f"Mock Success: Nodepool '{nodepool_id}' scaled to {num_nodes} nodes."

# --- SLS/ARMS Tools ---
@mcp.tool
def query_sls(query: str, sls_project: str, sls_logstore: str, time_range: str = "24h") -> str:
    """Queries logs from SLS using a SQL-like query."""
    logging.info(f"Querying SLS project '{sls_project}/{sls_logstore}' with: {query}")
    return "Mock Log Result: Found 5 logs containing 'invoked oom-killer'."

@mcp.tool
def query_arms(promql_query: str, arms_project: str, region_id: str) -> str:
    """Queries metrics from ARMS/Prometheus using PromQL."""
    logging.info(f"Querying ARMS project '{arms_project}' in region '{region_id}' with: {promql_query}")
    return "Mock Metric Result: container_memory_usage_bytes for 'app-pod-1-abcdef' shows a sharp increase to its limit before crashing."

if __name__ == "__main__":
    logging.info("Starting FastMCP SRE Tools Server...")
    mcp.run()