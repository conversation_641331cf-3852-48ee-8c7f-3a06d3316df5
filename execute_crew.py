import os
from typing import Callable, Any
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from mcp import StdioServerParameters
import yaml

load_dotenv()

def load_yaml(path: str) -> dict:
    with open(path, 'r') as file:
        return yaml.safe_load(file)

def create_execute_crew(inputs: dict, scenario: str = "oom_diagnose", step_callback: Callable[[Any], None] | None = None):
    """
    创建并运行执行 Crew，根据输入的指令进行操作。
    """
    agents_config = load_yaml(f'config/{scenario}/agents.yaml')
    tasks_config = load_yaml(f'config/{scenario}/tasks.yaml')

    server_configs = [StdioServerParameters(command="python", args=["mcp_server.py"])]
    
    with MCPServerAdapter(server_configs[0]) as mcp_tools:
        qwen_llm = LLM(
            model="openai/qwen-turbo-latest",
            base_url=os.getenv("QWEN_BASE_URL"),
            api_key=os.getenv("QWEN_API_KEY"),
            temperature=0.7,
        )

        # --- 只创建执行操作所需的代理 ---
        cluster_operator = Agent(
            **agents_config['cluster_operator'],
            tools=[mcp_tools["k8s_patch_deployment_resources"], mcp_tools["cs_autoscaler_scale_nodepool"]],
            llm=qwen_llm,
            verbose=True,
            step_callback=step_callback
        )

        # --- 创建执行任务 ---
        # 注意：这里的 description 将由 API 动态填充
        execution_task = Task(
            description=f"严格按照以下指令执行操作：\n---BEGIN INSTRUCTION---\n{inputs['instruction']}\n---END INSTRUCTION---",
            expected_output="一份简洁的执行报告，明确说明已执行的操作以及操作结果（成功或失败）。",
            agent=cluster_operator
        )
        
        crew = Crew(
            agents=[cluster_operator],
            tasks=[execution_task],
            process=Process.sequential, # 只有一个 agent，使用顺序流程即可
            verbose=True
        )
        
        # 执行任务时不需要额外的 inputs，因为指令已经包含所有信息
        result = crew.kickoff()
        return result