import requests
import time
import argparse
import json

API_BASE_URL = "http://127.0.0.1:8000"

def kickoff_diagnose(payload: dict) -> str:
    """启动诊断 Crew。"""
    response = requests.post(f"{API_BASE_URL}/diagnose", json=payload)
    response.raise_for_status()
    return response.json()["task_id"]

def kickoff_execute(payload: dict) -> str:
    """启动执行 Crew。"""
    print(f"  - 正在发送执行请求，负载: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    response = requests.post(f"{API_BASE_URL}/execute", json=payload)
    response.raise_for_status()
    return response.json()["task_id"]

def poll_status(task_id: str) -> dict:
    """轮询任务状态，直到任务完成或需要人工输入。"""
    while True:
        print(f"  - 正在查询任务 {task_id} 的状态...")
        response = requests.get(f"{API_BASE_URL}/status/{task_id}")
        response.raise_for_status()
        data = response.json()
        
        status = data.get("status")
        if status not in ["accepted", "running"]:
            return data
        
        time.sleep(5)

def main():
    parser = argparse.ArgumentParser(description="AI-Ops Crew CLI (Dual-Crew Architecture)")
    parser.add_argument("--scenario", type=str, default="oom_diagnose", help="The scenario to run")
    parser.add_argument("--namespace", type=str, default="default", help="Kubernetes namespace")
    args = parser.parse_args()

    # --- 步骤 1: 启动诊断 ---
    diagnose_payload = {
        "scenario": args.scenario,
        "inputs": {
            "namespace": args.namespace,
            "sls_project": "my-k8s-project",
            "sls_logstore": "container_logs",
            "arms_project": "my-arms-project",
            "regionId": "cn-hangzhou",
            "clusterID": "c123456789",
            "nodePoolID": "np123456789"
        }
    }
    print("🚀 步骤 1: 启动诊断 Crew...")
    diagnose_task_id = kickoff_diagnose(diagnose_payload)
    print(f"✅ 诊断任务已启动，ID: {diagnose_task_id}")

    diagnose_result = poll_status(diagnose_task_id)
    print(f"✅ 诊断完成，状态: {diagnose_result.get('status')}")

    # --- 步骤 2: 人工审批 ---
    if diagnose_result.get("status") == "pending_approval":
        proposal = diagnose_result.get("result")
        
        if not proposal:
            print("\n🔥 诊断未返回任何提议方案，无法继续。")
            return

        print("\n🤔 步骤 2: 需要您进行审批")
        print("========================================")
        print("提议的解决方案:")
        # CrewOutput 对象可能是一个复杂的对象，我们只打印它的 'raw' 部分
        instruction_text = proposal if isinstance(proposal, str) else proposal.get('raw', str(proposal))
        print(instruction_text)
        print("========================================")
        
        approval = input("您是否批准此方案? (yes/no): ").lower().strip()

        if approval == "yes":
            # --- 步骤 3: 启动执行 ---
            execute_payload = {
                "scenario": args.scenario,
                "instruction": instruction_text
            }
            print("\n🚀 步骤 3: 正在启动执行 Crew...")
            execute_task_id = kickoff_execute(execute_payload)
            print(f"✅ 执行任务已启动，ID: {execute_task_id}")

            execute_result = poll_status(execute_task_id)
            print("\n\n🏁 执行完成！")
            print("==========================")
            print(json.dumps(execute_result, indent=2, ensure_ascii=False))
        else:
            print("\n❌ 操作已取消。")
    elif diagnose_result.get("status") == "completed":
        print("\n\n🏁 诊断完成，无需执行操作。")
        print("==========================")
        print(json.dumps(diagnose_result, indent=2, ensure_ascii=False))
    else: # failed
        print("\n\n🔥 诊断失败。")
        print("==========================")
        print(json.dumps(diagnose_result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()