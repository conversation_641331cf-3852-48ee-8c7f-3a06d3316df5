# ===================================================================
#  任务配置
# 此结构使主管能够委派原子性、可并行的任务。
# ===================================================================

# --- 可委派的工作任务（用于诊断阶段） ---

k8s_investigation_task:
  description: >
    调查 '{namespace}' 命名空间下的 Kubernetes 集群，以查找潜在 OOM 问题的根本原因。
    
    你的工作流程应为：
    1. 首先，查找所有状态为 'OOMKilled' 或 'CrashLoopBackOff' 的 pod，生成目标列表。
    2. 然后，有条不紊地调查列表中的每个目标，通过 describe 获取事件和资源详情。
    
    当你调查完初始列表中的所有目标后，编写一份全面的报告。
  expected_output: >
    一份简明报告，列出所有发现的 OOMKilled/CrashLoopBackOff pod 的名称，
    并汇总每个 pod 的关键事件（如 OOMKilled 原因）和资源限制。

sls_log_analysis_task:
  description: >
    分析 SLS 日志。用户正在调查与 '{namespace}' 命名空间下 pod 相关的 OOM 问题。
    针对项目 '{sls_project}' 和日志库 '{sls_logstore}'，构建并执行自然语言查询，
    查找过去 24 小时内包含 'OOM'、'out of memory' 或 'oom-killer' 等词的日志。
  expected_output: >
    日志分析摘要。如果发现相关日志，请提供部分示例。
    如果没有，请明确说明未发现 OOM 相关日志。

arms_metric_analysis_task:
  description: >
    分析 '{namespace}' 命名空间下 pod 的 ARMS 指标，以查找潜在 OOM 原因。
    针对 ARMS 项目 '{arms_project}' 和区域 '{regionId}'，构建并执行 PromQL 查询，
    检查最近重启或内存使用率较高的 pod 的 'container_memory_usage_bytes'。
  expected_output: >
    指标分析摘要。特别说明是否有容器在重启前内存使用量接近或超过其限制。
# --- 可委派的工作任务（用于执行阶段） ---

cluster_remediation_task:
  description: >
    根据主管提供的精确、可执行的方案，对集群进行变更。
    方案内容将通过上下文明确提供。你必须严格按照方案执行，不得有任何偏差。
    例如，如果方案是“将 'app-deployment' 的内存限制更新为 '2Gi'”，
    你就需要调用相应的工具来完成此操作。
  expected_output: >
    一份简洁的执行报告，明确说明已执行的操作以及操作结果（成功或失败）。
    例如：“成功将 deployment 'app-deployment' 的内存限制更新为 '2Gi'。”