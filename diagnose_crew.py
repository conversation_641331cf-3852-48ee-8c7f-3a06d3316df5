import os
from typing import Callable, Any
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from mcp import StdioServerParameters
import yaml

load_dotenv()

def load_yaml(path: str) -> dict:
    with open(path, 'r') as file:
        return yaml.safe_load(file)

def create_diagnose_crew(inputs: dict, scenario: str = "oom_diagnose", step_callback: Callable[[Any], None] | None = None):
    """
    创建并运行诊断 Crew，返回提议方案。
    """
    agents_config = load_yaml(f'config/{scenario}/agents.yaml')
    tasks_config = load_yaml(f'config/{scenario}/tasks.yaml')

    server_configs = [StdioServerParameters(command="python", args=["mcp_server.py"])]
    
    with MCPServerAdapter(server_configs[0]) as mcp_tools:
        qwen_llm = LLM(
            model="openai/qwen-turbo-latest",
            base_url=os.getenv("QWEN_BASE_URL"),
            api_key=os.getenv("QWEN_API_KEY"),
            temperature=0.7,
        )

        # --- 只创建诊断和分析所需的代理 ---
        sre_supervisor = Agent(**agents_config['sre_supervisor'], llm=qwen_llm, verbose=True, step_callback=step_callback)
        k8s_diagnoser = Agent(**agents_config['k8s_diagnoser'], tools=[mcp_tools["k8s_get"], mcp_tools["k8s_describe"]], llm=qwen_llm, verbose=True, step_callback=step_callback)
        sls_diagnoser = Agent(**agents_config['sls_diagnoser'], tools=[mcp_tools["query_sls"]], llm=qwen_llm, verbose=True, step_callback=step_callback)
        arms_diagnoser = Agent(**agents_config['arms_diagnoser'], tools=[mcp_tools["query_arms"]], llm=qwen_llm, verbose=True, step_callback=step_callback)

        # --- 创建诊断和分析任务 ---
        k8s_task = Task(**tasks_config['k8s_investigation_task'], agent=k8s_diagnoser)
        sls_task = Task(**tasks_config['sls_log_analysis_task'], agent=sls_diagnoser)
        arms_task = Task(**tasks_config['arms_metric_analysis_task'], agent=arms_diagnoser)

        analysis_propose_task = Task(
            description="""分析来自 Kubernetes、SLS 和 ARMS 诊断任务收集的数据。
            基于综合证据，确定 OOMKill 问题的根本原因。
            如果需要修复，提出一个清晰、精确、可由另一个智能体直接执行的方案。
            如果未发现问题，请明确说明并给出长期建议。
            你的最终输出必须是可执行的方案或最终结论，不需要任何确认。""",
            expected_output="一份包含根因分析和精确、可直接执行的方案或长期建议的最终报告。",
            agent=sre_supervisor,
            context=[k8s_task, sls_task, arms_task]
        )
        
        crew = Crew(
            agents=[sre_supervisor, k8s_diagnoser, sls_diagnoser, arms_diagnoser],
            tasks=[k8s_task, sls_task, arms_task, analysis_propose_task],
            process=Process.hierarchical,
            manager_llm=qwen_llm,
            planning=True,
            planning_llm=qwen_llm,
            verbose=True
        )
        
        result = crew.kickoff(inputs=inputs)
        return result