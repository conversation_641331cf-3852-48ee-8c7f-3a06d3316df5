# AI-Ops 智能体优化计划 (V2)

本文档根据深入讨论和对 CrewAI 文档的再次研究进行了修订，旨在为 AI-Ops SRE 智能体的演进制定一个更精准、更具前瞻性的路线图。

## 1. 核心架构决策

经过深入分析，我们确定以下核心技术方向：

- **服务化架构**: 我们将采用 **FastAPI** 将 CrewAI 工作流封装成一个 RESTful 服务。这是实现未来扩展性、支持多种客户端（UI, CLI, 其他服务）并与外部系统集成的最佳选择。
- **异步任务管理**: 借鉴 CrewAI Enterprise API 的设计 (`[19] Start Crew Execution`, `[5] Get Execution Status`)，我们的服务将采用异步模式。客户端发起任务后立即获得任务 ID，并通过单独的端点查询状态或通过 Webhook (`[49] Webhook Streaming`) 接收更新。
- **模块化与可扩展性**: 架构的核心将是一个 `CrewManager`，它能根据不同的应用场景（如 OOM 诊断、成本分析）动态加载对应的智能体、任务和工具配置。这确保了在增加新功能时，我们只需添加配置文件和工具，而无需改动核心服务逻辑。

## 2. 修订后的优化路线图

### 阶段一：实现核心的闭环工作流
1.  **定义操作员任务**: 在 `config/tasks.yaml` 中为 `cluster_operator` 定义一个明确的、接受参数的执行任务（如 `apply_changes_task`）。这是形成“诊断-修复”闭环的关键。
2.  **实现 HITL 审批流**:
    - 利用 CrewAI 的原生人机协同能力 (`[103] Human-in-the-Loop (HITL) Workflows`)。
    - 在 `sre_supervisor` 提出方案后，增加一个需要人类输入的 `approval_task`。
3.  **实现条件任务**:
    - 利用 CrewAI 的条件执行能力 (`[95] Conditional Tasks`)。
    - 只有当 `approval_task` 获得批准时，才触发 `cluster_operator` 的执行任务。

### 阶段二：构建服务化架构
1.  **搭建 FastAPI 服务**: 创建一个基础的 FastAPI 应用，用于托管我们的 CrewAI 服务。
2.  **实现核心 API 端点**:
    - `POST /crews/kickoff`: 接收输入，启动一个 Crew，并返回一个 `task_id`。
    - `GET /crews/status/{task_id}`: 根据 `task_id` 查询并返回任务的当前状态和结果。
3.  **重构 `crew.py`**: 将 `crew.py` 中的逻辑拆分为服务层和 Crew 定义层，使其可以在 FastAPI 应用中被调用。

### 阶段三：提升架构的健壮性与易用性
1.  **构建一个简单的 CLI 客户端**: 创建一个独立的 Python 脚本，作为我们 FastAPI 服务的客户端。这个 CLI 将负责调用 API、提交任务并轮询结果，为 SRE 提供熟悉的交互方式。
2.  **完善架构的可扩展性**: 设计并实现 `CrewManager` 的动态加载机制，使其能够根据 API 请求中的 `scenario` 参数加载不同的配置。

## 3. 修订后的工作流 (Mermaid 图)

```mermaid
graph TD
    subgraph "Client (CLI/UI)"
        A[用户通过客户端发起诊断]
    end

    subgraph "FastAPI Service"
        B(POST /crews/kickoff)
        C[CrewManager 动态组建 Crew]
        D{Crew 异步执行}
        E(GET /crews/status/task_id)
    end
    
    subgraph "Crew Execution (内部流程)"
        F[并行诊断] --> G[主管分析 & 提议]
        G --> H{HITL: 请求审批}
        H -->|批准| I[执行变更]
        H -->|拒绝| J[结束]
    end

    A --> B
    B --> C
    C --> D
    A -- 轮询/Webhook --> E
    E --> A
    D -- 内部执行 --> F