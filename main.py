from fastapi import FastAP<PERSON>
from fastapi.responses import StreamingResponse
from chainlit.utils import mount_chainlit
from diagnose_crew import create_diagnose_crew
from execute_crew import create_execute_crew
from pydantic import BaseModel
from typing import Dict, Any
import asyncio
import queue
import json
from concurrent.futures import ThreadPoolExecutor

# --- Pydantic Models ---
class DiagnoseRequest(BaseModel):
    inputs: Dict[str, Any]

class ExecuteRequest(BaseModel):
    instruction: str

# --- FastAPI App ---
app = FastAPI()
executor = ThreadPoolExecutor(max_workers=5)

# --- Streaming Endpoints ---

@app.post("/api/diagnose-stream")
async def diagnose_stream(request: DiagnoseRequest):
    q = queue.Queue()

    def step_callback(output):
        """Callback to put agent step output into the queue."""
        # Handle different types of output from CrewAI
        if hasattr(output, 'output') and hasattr(output, 'agent') and hasattr(output, 'task'):
            data_to_send = {
                "output": str(output.output),
                "agent": str(output.agent),
                "task": str(output.task),
            }
            q.put(json.dumps(data_to_send))
        # You can add more specific handling for other output types if needed
        # For now, we'll just ignore the ones that don't have the expected attributes

    async def stream_generator():
        """Generator function to yield data from the queue."""
        loop = asyncio.get_event_loop()
        
        # Run the crew in a separate thread to avoid blocking
        future = loop.run_in_executor(
            executor, 
            create_diagnose_crew, 
            request.inputs, 
            "oom_diagnose", 
            step_callback
        )
        
        while True:
            try:
                # Non-blocking get from the queue
                chunk = q.get_nowait()
                yield f"data: {chunk}\n\n"
            except queue.Empty:
                # If the future is done, the crew has finished
                if future.done():
                    final_result = future.result()
                    # Extract the raw string output from the CrewOutput object
                    raw_output = final_result.raw if hasattr(final_result, 'raw') else str(final_result)
                    yield f"data: {json.dumps({'final_result': {'raw': raw_output}})}\n\n"
                    yield f"data: [DONE]\n\n"
                    break
                await asyncio.sleep(0.5) # Wait before trying again

    return StreamingResponse(stream_generator(), media_type="text/event-stream")

@app.post("/api/execute-stream")
async def execute_stream(request: ExecuteRequest):
    q = queue.Queue()

    def step_callback(output):
        """Callback to put agent step output into the queue."""
        if hasattr(output, 'output') and hasattr(output, 'agent') and hasattr(output, 'task'):
            data_to_send = {
                "output": str(output.output),
                "agent": str(output.agent),
                "task": str(output.task),
            }
            q.put(json.dumps(data_to_send))

    async def stream_generator():
        """Generator function to yield data from the queue."""
        loop = asyncio.get_event_loop()
        
        future = loop.run_in_executor(
            executor, 
            create_execute_crew, 
            {"instruction": request.instruction}, 
            "oom_diagnose", 
            step_callback
        )
        
        while True:
            try:
                chunk = q.get_nowait()
                yield f"data: {chunk}\n\n"
            except queue.Empty:
                if future.done():
                    final_result = future.result()
                    raw_output = final_result.raw if hasattr(final_result, 'raw') else str(final_result)
                    yield f"data: {json.dumps({'final_result': {'raw': raw_output}})}\n\n"
                    yield f"data: [DONE]\n\n"
                    break
                await asyncio.sleep(0.5)

    return StreamingResponse(stream_generator(), media_type="text/event-stream")

# --- Mount Chainlit App ---
mount_chainlit(app=app, target="chainlit_app.py", path="/")