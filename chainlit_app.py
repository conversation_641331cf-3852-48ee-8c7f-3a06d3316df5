import chainlit as cl
import httpx
import json

API_BASE_URL = "http://127.0.0.1:8000"

async def process_stream(ui_message: cl.Message, response: httpx.Response):
    """处理来自后端的流式响应，并实时更新UI。"""
    final_result = None
    async for line in response.aiter_lines():
        if line.startswith("data:"):
            data_str = line[len("data:"):].strip()
            if data_str == "[DONE]":
                await ui_message.send() # Finalize the message
                break
            
            try:
                data = json.loads(data_str)
                if "final_result" in data:
                    final_result = data["final_result"]
                elif "output" in data:
                    # Stream the intermediate steps to the UI
                    await ui_message.stream_token(f"\n\n- **{data.get('agent', 'Agent')}**: {data.get('output', '')}")
            except json.JSONDecodeError:
                # Handle cases where data_str is not valid JSON
                await ui_message.stream_token(f"\n\n- {data_str}")

    return final_result

@cl.on_chat_start
async def on_chat_start():
    await cl.Message(content="欢迎使用 AI-Ops SRE 助手！请输入您要诊断的 Kubernetes namespace:").send()

@cl.on_message
async def on_message(message: cl.Message):
    namespace = message.content
    ui_message = cl.Message(content=f"好的，正在开始诊断 namespace: `{namespace}`...")
    await ui_message.send() # Initial message

    diagnose_payload = {
        "inputs": {
            "namespace": namespace,
            "sls_project": "my-k8s-project",
            "sls_logstore": "container_logs",
            "arms_project": "my-arms-project",
            "regionId": "cn-hangzhou",
            "clusterID": "c123456789",
            "nodePoolID": "np123456789"
        }
    }
    
    async with httpx.AsyncClient(timeout=None) as client:
        async with client.stream("POST", f"{API_BASE_URL}/api/diagnose-stream", json=diagnose_payload) as response:
            diagnose_result = await process_stream(ui_message, response)

    if diagnose_result:
        proposal = diagnose_result.get("raw", "没有找到提议方案。")
        cl.user_session.set("proposal", proposal)

        res = await cl.AskActionMessage(
            content=f"**诊断完成，提议的解决方案如下:**\n\n---\n\n{proposal}\n\n---\n\n**您是否批准此方案?**",
            actions=[
                cl.Action(name="approve", label="✅ 批准执行", payload={"value": "yes"}),
                cl.Action(name="deny", label="❌ 拒绝", payload={"value": "no"}),
            ],
            timeout=300
        ).send()

        if res and res.get("payload").get("value") == "yes":
            await on_approve()
        else:
            await on_deny()
    else:
        await cl.Message(content="诊断完成，但未收到明确的最终结果。").send()

async def on_approve():
    ui_message = cl.Message(content="✅ **方案已批准**。正在启动执行 Crew...")
    await ui_message.send()
    
    proposal = cl.user_session.get("proposal")
    
    execute_payload = {"instruction": proposal}
    async with httpx.AsyncClient(timeout=None) as client:
        async with client.stream("POST", f"{API_BASE_URL}/api/execute-stream", json=execute_payload) as response:
            execute_result = await process_stream(ui_message, response)
    
    final_report = execute_result.get("raw", "执行完毕，无详细输出。") if execute_result else "执行完毕，无详细输出。"
    await cl.Message(content=f"**执行完成！**\n\n---\n\n{final_report}").send()

async def on_deny():
    await cl.Message(content="❌ **操作已取消**。").send()